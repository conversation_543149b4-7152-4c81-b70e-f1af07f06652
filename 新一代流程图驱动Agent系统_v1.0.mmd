graph TD
    %% 新一代流程图驱动Agent系统 v1.0
    %% 基于ChatGPT Agent技术的智能提示词系统
    
    %% 输入层
    A[👤 用户输入] --> B{🛡️ 安全检查}
    
    %% 安全检查分支
    B -->|✅ 通过| C[📋 任务分类器]
    B -->|❌ 阻断| Z1[🚨 安全报告]
    B -->|⚠️ 警告| Z2[⚠️ 风险提示]
    
    %% 任务分类
    C --> D{🎯 复杂度评估}
    
    %% 复杂度分支
    D -->|🚀 简单任务| E[⚡ 快速执行通道]
    D -->|🧠 复杂任务| F[🌳 决策树查询]
    
    %% 快速执行通道
    E --> E1[📝 变量重命名]
    E --> E2[🔧 拼写修正]
    E --> E3[📄 简单格式化]
    E1 --> QC1{✅ 快速质检}
    E2 --> QC1
    E3 --> QC1
    
    %% 决策树查询
    F --> G{🎭 任务类型识别}
    
    %% 任务类型分支
    G -->|🔍 Research| H[📚 研究模块]
    G -->|💻 Code| I[⚙️ 代码模块]
    G -->|🛠️ Tool| J[🔧 工具模块]
    G -->|🔗 Integration| K[🌐 集成模块]
    G -->|🎨 Mixed| L[🎭 混合模式]
    
    %% 研究模块流程
    H --> H1[🌐 信息搜集<br/>Tavily + GitHub + Context7]
    H1 --> H2[📊 数据分析<br/>交叉验证 + 可信度评估]
    H2 --> H3[📋 结果整合<br/>结构化输出 + 引用标注]
    
    %% 代码模块流程
    I --> I1[📋 需求分析<br/>功能拆解 + 架构设计]
    I1 --> I2[💻 代码生成<br/>多语言支持 + 最佳实践]
    I2 --> I3[🧪 功能验证<br/>单元测试 + 集成测试]
    
    %% 工具模块流程
    J --> J1[🔍 工具识别<br/>能力匹配 + 兼容性检查]
    J1 --> J2[⚙️ 工具配置<br/>参数设置 + 环境准备]
    J2 --> J3[▶️ 工具执行<br/>监控运行 + 异常处理]
    
    %% 集成模块流程
    K --> K1[🔗 系统整合<br/>接口设计 + 数据流规划]
    K1 --> K2[🔄 接口同步<br/>版本控制 + 兼容性测试]
    K2 --> K3[📦 系统打包<br/>部署准备 + 文档生成]
    
    %% 混合模式流程
    L --> L1[🎯 任务分解<br/>模块识别 + 依赖分析]
    L1 --> L2[🔄 并行执行<br/>多模块协调 + 进度同步]
    L2 --> L3[🔗 结果合并<br/>数据整合 + 一致性检查]
    
    %% 汇聚到质量检查
    H3 --> QC2{🎯 四维质量检查}
    I3 --> QC2
    J3 --> QC2
    K3 --> QC2
    L3 --> QC2
    QC1 --> QC2
    
    %% 质量检查详细
    QC2 --> QC2A[✅ 功能完整性]
    QC2 --> QC2B[📝 代码质量]
    QC2 --> QC2C[⚡ 性能指标]
    QC2 --> QC2D[👤 用户体验]
    
    QC2A --> QC3{🎯 综合评估}
    QC2B --> QC3
    QC2C --> QC3
    QC2D --> QC3
    
    %% 质量检查结果分支
    QC3 -->|✅ 全部通过| Y[🎉 输出结果]
    QC3 -->|❌ 部分失败| AA[🔧 错误处理引擎]
    
    %% 错误处理引擎
    AA --> BB{🔍 错误类型识别}
    
    %% 错误类型分支
    BB -->|📝 语法错误| CC1[🔧 自动修复]
    BB -->|🏃 运行错误| CC2[⚠️ 异常处理]
    BB -->|🛠️ 工具错误| CC3[🔄 切换工具]
    BB -->|🧠 逻辑错误| CC4[🎯 重新决策]
    
    %% 重试计数检查
    CC1 --> DD1{🔢 重试≤3次?}
    CC2 --> DD2{🔢 重试≤2次?}
    CC3 --> DD3{🔢 重试≤1次?}
    CC4 --> DD4{🔢 重试≤2次?}
    
    %% 重试决策
    DD1 -->|✅ 是| EE1[🔄 执行语法修复]
    DD1 -->|❌ 否| FF1[❌ 语法修复失败]
    DD2 -->|✅ 是| EE2[🔄 执行异常处理]
    DD2 -->|❌ 否| FF2[❌ 运行处理失败]
    DD3 -->|✅ 是| EE3[🔄 执行工具切换]
    DD3 -->|❌ 否| FF3[❌ 工具切换失败]
    DD4 -->|✅ 是| EE4[🔄 执行重新决策]
    DD4 -->|❌ 否| FF4[❌ 逻辑决策失败]
    
    %% 修复后返回主流程
    EE1 --> C
    EE2 --> C
    EE3 --> C
    EE4 --> C
    
    %% 失败报告
    FF1 --> GG[📋 失败报告生成]
    FF2 --> GG
    FF3 --> GG
    FF4 --> GG
    
    %% 成功输出
    Y --> HH[📊 性能统计]
    HH --> II[💾 经验存储]
    II --> JJ[🔄 系统优化]
    
    %% 样式定义
    classDef inputClass fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef securityClass fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef processClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef moduleClass fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef qualityClass fill:#fff8e1,stroke:#f57f17,stroke-width:2px
    classDef errorClass fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef outputClass fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    
    %% 应用样式
    class A inputClass
    class B,Z1,Z2 securityClass
    class C,D,F,G processClass
    class H,I,J,K,L,H1,H2,H3,I1,I2,I3,J1,J2,J3,K1,K2,K3,L1,L2,L3 moduleClass
    class QC1,QC2,QC2A,QC2B,QC2C,QC2D,QC3 qualityClass
    class AA,BB,CC1,CC2,CC3,CC4,DD1,DD2,DD3,DD4,EE1,EE2,EE3,EE4,FF1,FF2,FF3,FF4,GG errorClass
    class Y,HH,II,JJ outputClass
